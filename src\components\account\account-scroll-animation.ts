import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import OnInit from '@core/decorators/init-method.decorator';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';

class AccountBlockScrollAnimation extends StratisElementAbstract {
  public options: IGlobalOptions<AccountBlockScrollAnimation> = {
    classList: {},
    dataset: {},
    DOMElements: {},
  };

  private parentElement!: HTMLElement;
  private stuckIn: 'bottom' | 'top' | null = null;

  private positionState: {
    scrollNumber: number;
    offsetToWindowTop: number;
    offsetToBlockTop: number;
    blockScrollStartOffset: number;
  } = {
    scrollNumber: -1,
    offsetToWindowTop: 0,
    offsetToBlockTop: 0,
    blockScrollStartOffset: 0,
  };

  public constructor(element: HTMLElement, options: Partial<IGlobalOptions<AccountBlockScrollAnimation>>, customName?: string) {
    super(element, options || {}, customName);

    if (!this.created) {
      this.init();
    }
  }

  protected getDOMElements(): void {
    super.getDOMElements();
    this.parentElement = this.element.parentElement!;
  }

  protected createEvents(): void {
    super.createEvents([
      [window, 'scroll', this.handleWindowScroll],
    ]);
  }

  private handleWindowScroll(): void {
    this.changePositionState();
    this.animateScroll();
  }

  /**
  * Set new position on scroll
  */
  @OnInit()
  private changePositionState(): void {
    this.positionState.offsetToWindowTop = this.element.getBoundingClientRect().top;
    this.positionState.offsetToBlockTop = this.parentElement.getBoundingClientRect().top;
  }

  /**
   * Scroll page to top
   */
  @OnInit()
  private scrollToTop(): void {
    window.scrollTo({ top: 0 });
  }

  /**
   * Set fixed position to element
   */
  private makeElementFixed(): void {
    this.element.style.position = 'fixed';
    this.element.style.top = '50%';
    this.element.style.transform = 'translateY(-50%)';
  }

  /**
   * Set relative position
   */
  private moveElementToTop(): void {
    this.element.style.position = 'relative';
    this.element.style.top = 'unset';
    this.element.style.transform = 'unset';
  }

  /**
   * Set absolute position and move element to bottom
   */
  private moveElementToBottom(): void {
    this.element.style.position = 'absolute';
    this.element.style.top = 'unset';
    this.element.style.bottom = '0';
    this.element.style.transform = 'unset';
  }


  /**
   * Set element width and height on load
   */
  @OnInit()
  private setElementSize(): void {
    this.element.style.width = `${this.element.offsetWidth}px`;
    this.element.style.height = `${this.element.offsetHeight}px`;
  }

  /**
   * Manage element position
   */
  private animateScroll(): void {
    const { innerHeight } = window;
    const { offsetToWindowTop, blockScrollStartOffset, offsetToBlockTop } = this.positionState;

    const offsetToCenter = innerHeight / 2 - (offsetToWindowTop + this.element.offsetHeight / 2);
    const isElementInCenter = this.stuckIn === 'top' ? offsetToCenter >= 0 : offsetToCenter <= 0;

    if (isElementInCenter && offsetToBlockTop < 400) {
      this.positionState.blockScrollStartOffset = blockScrollStartOffset || offsetToBlockTop;
    }

    if (offsetToBlockTop - blockScrollStartOffset > window.innerHeight - 980) {
      this.moveElementToTop();
      this.stuckIn = 'top';
    } else if (offsetToBlockTop * -1 + innerHeight / 3 >= this.parentElement.offsetHeight - this.element.offsetHeight + 50) {
      this.moveElementToBottom();
      this.stuckIn = 'bottom';
    } else if (isElementInCenter && offsetToBlockTop < 400) {
      this.makeElementFixed();
    }
  }

  public destroy(): void {
    super.destroy();
    this.element.style.width = 'auto';
    this.element.style.height = 'auto';
    this.moveElementToTop();
  }
}

const AccountBlockScrollAnimationFactory = StratisFactoryMixin<typeof AccountBlockScrollAnimation, AccountBlockScrollAnimation, IGlobalOptions<AccountBlockScrollAnimation>>(AccountBlockScrollAnimation);
export default AccountBlockScrollAnimationFactory;
