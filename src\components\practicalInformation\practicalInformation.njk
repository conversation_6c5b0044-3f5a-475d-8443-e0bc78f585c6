{%- from 'views/utils/utils.njk' import svg -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    PracticalInformationItem template.
#}
{%- macro PracticalInformationItem(icon, title, subtitle) -%}
    <div class="practical-information__item">
        <div class="practical-information__item-svg" aria-hidden="true">
            {{ svg('icons/'+icon, 38, 40) }}
        </div>
        <div class="practical-information__item-title">{{ title }}</div>
        <div class="practical-information__item-subtitle">{{ subtitle }}</div>
    </div>
{%- endmacro -%}

{#
    PracticalInformation template.
#}
{%- macro PracticalInformation(
    title= "Informations pratiques",
    teaser= "<p>Tout public. Également accessible aux personnes en situation de handicap.</p><p>En tant que personne en situation de handicap, vous avez accès à nos formations. Pour plus de renseignements, contactez notre référente handicap.</p><p>Catherine <PERSON>LERGEREAUX <EMAIL></p>",
    items= [
        { icon: 'clock', title: 'Durée indicative :', subtitle: '1633 heures soit 49 semaines dont 6 semaines de stages en entreprise(s).' },
        { icon: 'circle-euro', title: 'Tarif :', subtitle: '9000€ ou voir sur Mon Compte Formation' },
        { icon: 'file-certificate', title: 'Certification visée :', subtitle: 'Titre Professionnel de niveau 4 délivré par le Ministère du Travail.' },
        { icon: 'badge-check', title: 'Prérequis :', subtitle: 'Savoir lire, écrire et compter.' },
        { icon: 'people-simple', title: 'Nombre de places :', subtitle: '10' },
        { icon: 'arrow-progress', title: 'Parcours individualisable', subtitle: '' },
        { icon: 'plus', title: 'Lorem ipsum', subtitle: '' }
    ]
) -%}
    <div class="practical-information">
        <div class="practical-information__infos">
            <div class="practical-information__title">{{ title }}</div>
            <div class="practical-information__teaser">
                <p>Tout public. Également accessible aux personnes en situation de handicap.</p>
                <p>En tant que personne en situation de handicap, vous avez accès à nos formations. Pour plus de renseignements, contactez notre référente handicap.</p>
                <p><NAME_EMAIL></p>
            </div>
        </div>
        <div class="practical-information__items">
            {% for item in items %}
                {{ PracticalInformationItem(icon = item.icon, title = item.title, subtitle = item.subtitle) }}
            {% endfor %}
        </div>
    </div>
{%- endmacro -%}
