{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/core-components/image.njk' import Image %}

{%- macro BannerItem(
    isReverse = false,
    isInverted = false,
    bgColor = '#7cba3d',
    title = 'Abonnez-vous à notre newsletter',
    imageSizes = ['320x135?479', '600x250']
    ) -%}
    <div class="banner-item {{ 'is-reverse' if isReverse }} {{ 'is-inverted' if isInverted }}" style="background-color: {{ bgColor }};">
        <div class="banner-item__content">
            <h2 class="banner-item__title">
                <a href="#" class="banner-item__title-link">
                    <span class="underline js-inline-bg banner-item__title-text" data-max-length="12">{{ title }}</span>
                </a>
            </h2>
            {{ Icon('far fa-long-arrow-right') }}
        </div>
        {{ Image({
            className: 'banner-item__image',
            sizes: imageSizes,
            serviceID: range(100) | random,
            alt: 'image alt'
        }) }}
    </div>
{%- endmacro -%}

{%- macro BannerHome(
    isReverse = false,
    isInverted = false,
    bgColor = '#7cba3d'
    ) -%}
    {% call Section(className = 'banner-home', container = 'banner-home__container') %}
        <div class="section__content banner-home__content">
            {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{%- macro BannerContent(
    className = 'banner-content',
    isReverse = false,
    isInverted = false,
    bgColor = '#7cba3d',
    imageSizes = ['320x135?479', '600x250']
    ) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__content">
            {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor,
                imageSizes = imageSizes
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}
