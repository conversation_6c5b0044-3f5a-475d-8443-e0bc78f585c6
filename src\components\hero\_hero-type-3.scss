.hero {
    &.is-type-3 {
        @include size(100%, 658px);
        margin-top: 143px;
        position: relative;

        @include breakpoint(medium down) {
            height: 333px;
            margin-top: 112px;
        }

        @include breakpoint(small down) {
            height: 100%;
            margin-top: 67px;
        }

        &::before {
            @include size(489px, 100%);
            @include absolute(0, null, null, 0);
            background-color: var(--color-1--1);
            content: "";
            mix-blend-mode: multiply;
            z-index: 1;

            @include breakpoint(medium down) {
                width: 284px;
            }

            @include breakpoint(small down) {
                transform: translate(-20px);
                width: 58vw;
            }
        }

        &::after {
            @include breakpoint(small down) {
                @include size(43px, 7px);
                @include absolute(244px, null, null, 0);
                background-color: var(--color-2--1);
                content: "";
                z-index: 1;
            }
        }
        
        .hero-item {
            &__content {
                @extend %link-block-context;
                @extend %underline-context;
                @include trs;
                @include absolute(null, null, 119px, 221px);
                box-sizing: border-box;
                color: $color-white;
                display: block;
                font-size: 3.5rem;
                font-weight: var(--fw-normal);
                max-width: 1240px;
                overflow: hidden;
                padding: 0;
                text-decoration: none;
                text-shadow: 0 0 6px rgba($color-black, 0.16);
                transform: translateX(-50%);
                width: 330px;
                z-index: 1;

                @include breakpoint(medium down) {
                    bottom: 75px;
                    left: 120px;
                    width: 193px;
                }

                @include breakpoint(small down) {
                    @include relative(auto, auto, auto, auto);
                    display: flex;
                    padding: 0;
                    transform: none;
                    width: 100%;
                    // padding: 19px 45px 18px 0;
                }

                &::before {
                    @include absolute(15px, 15px, 15px, 15px);
                    @include trs;
                    border: 2px solid rgba($color-white, 0.5);
                    content: '';
                    opacity: 0;
                    visibility: hidden;

                    @include breakpoint(small down) {
                        content: none;
                    }
                }

                @include on-event {
                    &[href] {
                        &::before {
                            @include absolute(0, 0, 0, 0);
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }

            &__category {
                color: $color-white;
                margin-bottom: 20px;
            }

            &__title {
                @include font(null, 3.5rem, var(--fw-normal));
                color: $color-white;
                line-height: 1.1;
                padding-bottom: 31px;

                @include breakpoint(medium down) {
                    font-size: 2rem;
                    line-height: 1.1;
                    padding-bottom: 36px;
                }

                @include breakpoint(small down) {
                    background-color: var(--color-1--1);
                    font-size: 1.4rem;
                    line-height: 15px;
                    padding-bottom: 0;
                    padding-right: 32px;
                    padding-top: 19px;
                    width: 52vw;
                }
            }

            &__picture {
                &::after {
                    height: 705px;

                    @include breakpoint(medium down) {
                        height: 488px;
                    }

                    @include breakpoint(small down) {
                        height: 368px;
                    }
                }
            }
        }
    }
}
