import { addClass } from '@core/utils/class.utils';
import { getNextFocusableElement } from '@core/utils/a11y.utils';

/**
 * Class represents breadcrumbs component
 */
export default class Breadcrumbs {
    private readonly toggles: NodeListOf<HTMLElement> | HTMLElement[] | null = null;
    private readonly $handleClick;
    private readonly $responsiveHandler;

    /**
     * Breadcrumbs constructor.
     * @param toggle {HTMLElement | string} - toggle element
     */
    constructor(toggle: HTMLElement | string = '.js-breadcrumbs-items-toggle') {
        if (toggle instanceof HTMLElement) {
            this.toggles = [toggle] as HTMLElement[];
        } else if (typeof toggle === 'string') {
            this.toggles = document.querySelectorAll(toggle);
        }


        // Bindings
        this.$handleClick = this.handleClick.bind(this);
        this.$responsiveHandler = this.responsiveHandler.bind(this);

        if (this.toggles && this.toggles.length) {
            this.init();
        }
    }

    /**
     * Init functionality
     * @private
     */
    private init(): void {
        if (this.toggles) {
            this.toggles.forEach((toggle: HTMLElement) => {
                const controlledItems = [...toggle.parentElement!.parentElement!.querySelectorAll<HTMLElement>('li[aria-hidden]')];

                if (controlledItems && controlledItems.length) {
                    this.initEvents(toggle, controlledItems);
                }
            });
        }
    }

    /**
     * Init plugin events.
     * @private
     */
    private initEvents(toggle: HTMLElement, controlledItems: HTMLElement[]): void {
        toggle.addEventListener('click', event => this.handleClick(event, controlledItems));
        window.addEventListener('responsive', () => this.responsiveHandler(controlledItems, toggle));
    }


    /**
     * Toggle click handler
     * @private
     */
    private handleClick(event: Event, controlledItems: HTMLElement[]): void {
        const toggle = event.currentTarget as HTMLElement;
        if (controlledItems.length) {
            controlledItems.forEach(breadcrumb => {
                addClass(breadcrumb, 'is-visible');
                breadcrumb.setAttribute('aria-hidden', 'false');
            });

            toggle.setAttribute('aria-expanded', 'true');
            addClass(toggle.parentElement!, 'is-hidden');

            const focusableElement = getNextFocusableElement(toggle, toggle.parentElement!.parentElement!);

            if (focusableElement) {
                focusableElement.focus();
            }
        }
    }

    /**
     * Manage attributes on responsive event.
     * @private
     */
    private responsiveHandler(controlledItems: HTMLElement[], toggle: HTMLElement): void {
        if (controlledItems.length) {
            controlledItems.forEach(breadcrumb => {
                const styles = getComputedStyle(breadcrumb);
                const isHidden = styles.display === 'none';
                breadcrumb.setAttribute('aria-hidden', isHidden.toString());
            });
        }

        if (toggle) {
            const hiddenItems = controlledItems.filter(item => item.getAttribute('aria-hidden') === 'true');
            toggle.setAttribute('aria-expanded', Boolean(!hiddenItems.length).toString());
        }
    }
}
