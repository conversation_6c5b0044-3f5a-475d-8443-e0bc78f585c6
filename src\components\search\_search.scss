.search {
    height: 100%;

    * {
        @include on-event() {
            border-bottom-color: var(--color-1--2);
        }
    }

    &__toggle {
        @extend %button;
        @extend %button-style-only-icon;
        @include trs;
        @include size(242px, 52px);
        border: 0;
        border-bottom: 1px solid $color-3--7;
        border-radius: 0;
        color: var(--color-1--2);
        min-height: auto;

        @include breakpoint(small down) {
            @include size(50px);
            border: 1px solid $color-3--7;
            min-height: 50px;
        }

        @include on-event {
            background-color: transparent;

            @include fa-icon-style(false) {
                animation: wiggle-animation forwards 0.8s;
            }
        }

        .form__field-wrapper {
            margin: 0;
            z-index: -1;

            @include breakpoint(medium down) {
                margin: 7px 0 0;
            }

            @include breakpoint(small down) {
                display: none;
            }

            input {
                background-color: transparent;
                border: 0;
                font-size: 1.6rem;
                min-height: auto;
                padding: 0;
    
                &::placeholder {
                    color: var(--color-1--2);
                    font-size: 1.6rem;
                }
            }
        }

        @include fa-icon-style(false) {
            color: var(--color-1--2);
            font-size: 2rem;
            margin-left: 23px;

            @include breakpoint(medium down) {
                margin: 6px 7px 0 13px;
            }

            @include breakpoint(small down) {
                margin: 0;
            }
        }
    }
}

@keyframes wiggle-animation {
    from {
        transform: rotate(0);
    }

    25% {
        transform: rotate(30deg);
    }

    60% {
        transform: rotate(-30deg);
    }

    100% {
        transform: rotate(0);
    }
}
