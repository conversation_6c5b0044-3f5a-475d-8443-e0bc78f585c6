.presentation-home {
    &.section {
        margin-bottom: 0;
        margin-top: 0;
        max-width: 1016px;
        min-width: 840px;

        @include breakpoint(medium down) {
            margin-top: 0;
            min-width: auto;
            position: relative;
            width: 100%;
        }
    }

    &__title {
        align-items: flex-start;
        flex-direction: column;
        margin-left: 546px;
        margin-top: 21px;
        position: relative;
        text-align: left;
        width: 231px;

        @include breakpoint(medium down) {
            margin-left: 429px;
            margin-top: 0;
        }

        @include breakpoint(small down) {
            margin-left: 0;
            width: 100%;
        }

        .subtitle-text {
            color: var(--color-1--1);
            font-size: 2.2rem;
            line-height: 52px;
            margin-bottom: 9px;

            @include breakpoint(medium down) {
                font-size: 2rem;
                line-height: 25px;
                margin-bottom: 33px;
                margin-top: -2px;
            }

            @include breakpoint(small down) {
                margin-bottom: 27px;
                margin-left: 19px;
                margin-top: 7px;
            }
        }

        .title:not(.rte).is-primary .title__text {
            &::before {
                content: none;
            }
        }

        .title {
            color: $color-white !important;
            font-size: 3.8rem;
            font-weight: var(--fw-bold);
            position: relative;

            &::after {
                @include size(540px, calc(100% + 36px));
                @include absolute(-17px, -48px, null, null);
                background-color: var(--color-1--1);
                content: "";
                display: block;
                mix-blend-mode: multiply;
                z-index: 1;

                @include breakpoint(medium down) {
                    @include size(424px, calc(100% + 42px));
                    right: -44px;
                    top: -21px;
                }

                @include breakpoint(small down) {
                    @include size(calc(100vw - 20px), 219px);
                    left: 0;
                    mix-blend-mode: normal;
                    right: auto;
                    top: -21px;
                    z-index: 0;
                }
            }

            &__content {
                font-weight: var(--fw-bold);
                position: relative;
                z-index: 2;

                @include breakpoint(small down) {
                    margin-left: 18px;
                    text-align: left !important;
                }
            }
        }
    }

    .section {
        &__more-links {
            @include absolute(null, null, 26px, 37px);
            justify-content: flex-start;
            padding-left: 510px;

            @include breakpoint(medium down) {
                bottom: 11px;
                padding-left: 392px;
            }

            @include breakpoint(small down) {
                justify-content: flex-start;
                left: 21px;
                padding-left: 0;
                position: relative;
            }

            .btn {
                background-color: transparent;
                border-radius: 4px;
                color: var(--color-1--1);
                font-size: 1.4rem;
                line-height: 18px;
                min-height: auto;
                padding: 10px 19px;
                text-transform: none;

                @include on-event() {
                    color: $color-white;
                }

                &__text {
                    letter-spacing: 0 !important;
                }
            }
        }
    }

    .presentation-item {
        flex-wrap: wrap;

        @include breakpoint(small down) {
            margin-bottom: 4px;
        }
    }
}

.presentation-keynumber-home {
    &.section {
        margin-bottom: 0;
        margin-top: 172px;
        max-width: 1600px;
        position: relative;

        @include breakpoint(medium down) {
            margin-top: 45px;
        }

        @include breakpoint(small down) {
            margin-top: 47px;
        }
    }

    &::before {
        @include size(100vw, 606px);
        @include absolute(0, null, null, calc(50% + 197px));
        background-color: $color-3--1;
        content: "";
        z-index: -1;

        @include breakpoint(medium down) {
            content: none;
        }
    }

    &__container {
        box-sizing: border-box;
        display: flex;
        margin: 0 auto 0 0;
        max-width: 1430px;
        padding: 0;
        width: 100%;

        @include breakpoint(medium down) {
            flex-direction: column;
            position: relative;
        }
    }

    .key-box-list {
        padding: 73px 0 0 70px;
        position: relative;
        width: 584px;

        @include breakpoint(medium only) {
            display: flex;
            padding-left: 60px;
            padding-top: 10px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 53px 0 0 28px;
            width: 360px;
        }

        &::before {
            @include breakpoint(medium down) {
                @include size(100vw, calc(100% + 69px));
                @include absolute(-69px, null, null, 0);
                background-color: $color-3--1;
                content: "";
                z-index: -1;
            }

            @include breakpoint(small down) {
                height: 100%;
                top: 0;
            }
        }

        .list__item {
            margin: 0 0 58px;
            padding: 0;

            @include breakpoint(medium down) {
                margin-right: 31px;
                width: 204px;
            }

            @include breakpoint(small down) {
                margin-bottom: 69px;
            }
        }
    }

    .key-box-item {
        align-items: flex-start;
        font-family: var(--typo-1);
        padding-left: 107px;
        position: relative;

        @include breakpoint(medium only) {
            padding-left: 0;
        }

        &__link {
            text-decoration: none;
        }

        &__icon {
            @include absolute(-11px, null, null, -10px);
            @include size(110px, 89px);
            
            @include breakpoint(medium only) {
                @include size(120px, 91px);
                left: -42px;
                margin-left: 27px;
                position: relative;
            }

            @include breakpoint(small down) {
                top: -22px;
            }
        }

        &__title-wrapper {
            background-color: transparent;
            margin: 0 0 3px;
            padding: 0;

            @include breakpoint(medium down) {
                margin: -14px 0 5px;
            }
        }

        &__title {
            @include font(var(--typo-1), 3.5rem, var(--fw-black));
            color: $color-3--6;
            line-height: 44px;
        }

        &__description {
            color: var(--color-1--5);
            font-size: 1.9rem;
            font-weight: var(--fw-medium);
            line-height: 2.4rem;
            position: relative;
            text-align: left;

            &::after {
                @include absolute(null, null, -20px, 0);
                @include size(35px, 4px);
                background-color: var(--color-1--5);
                content: "";

                @include breakpoint(medium down) {
                    bottom: -22px;
                }
            }
        }
    }
}
