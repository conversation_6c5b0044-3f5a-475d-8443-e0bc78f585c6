.account-greeting {
    margin: 35px 0 100px;

    @include breakpoint(medium down) {
        margin-bottom: 60px;
    }

    @include breakpoint(small down) {
        margin-bottom: 30px;
    }

    &__wrapper {
        @extend %container;
        align-items: center;
        display: flex;
        justify-content: space-between;

        @include breakpoint(medium down) {
            align-items: center;
            flex-direction: column;
            justify-content: flex-start;
        }
    }

    &__info {
        max-width: 700px;
        padding-left: 80px;

        @include breakpoint(medium down) {
            margin: 0 0 58px;
            padding-left: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 30px;
        }
    }

    &__title {
        @include font(null, 4.5rem, var(--fw-bold));
        line-height: 1.25;
        margin-bottom: 30px;

        @include breakpoint(medium down) {
            font-size: 3.5rem;
            text-align: center;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
        }

        strong {
            color: var(--color-1--1);
        }
    }

    &__description {
        color: $color-3--4;
        font-size: 2.4rem;
        font-weight: var(--fw-bold);
        line-height: 3.6rem;

        @include breakpoint(medium down) {
            text-align: center;
        }

        @include breakpoint(small down) {
            font-size: 1.8rem;
            line-height: 2.6rem;
        }
    }

    &__picture {
        @include size(222px, 214px);
        margin: 0 auto;

        @include breakpoint(medium only) {
            @include size(272px, 262px);
        }

        svg {
            @include size(100%);

            .color-1--1 {
                fill: var(--color-1--1);
            }

            .color-1--2 {
                fill: var(--color-1--2);
            }

            .color-1--3 {
                fill: var(--color-1--3);
            }

            .color-1--4 {
                fill: var(--color-1--4);
            }

            .color-2--1 {
                fill: var(--color-2--1);
            }

            .color-2--2 {
                fill: var(--color-2--2);
            }

            .color-2--3 {
                fill: var(--color-2--3);
            }
        }
    }
}
