import { key, visibleArea } from 'ally.js/when/_when';
import { focusable, tabbable } from 'ally.js/query/_query';
import l10n from '@core/lang/locallang';
import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import OnInit from '@core/decorators/init-method.decorator';
import { HTMLElementMap } from '@core/interfaces/base-types';
import OnDestroy from '@core/decorators/destroy-method.decorator';
import { getNextFocusableElement, getPrevFocusableElement } from '@core/utils/a11y.utils';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import { setAttributes } from '@core/utils/attr.utils';

interface IBookingOptions extends IGlobalOptions<Booking> {
    startDateField: string;
    endDateField: string;
    onContentUpdate?(): void;
}

/**
 * Class represents Booking component
 */
export class Booking extends StratisElementAbstract {
    protected options: IBookingOptions = {
        classList: {
            period: 'booking__period',
            day: 'booking__day',
            list: 'booking__list',
            listWrapper: 'booking__list-wrap',
            carousel: 'js-booking-carousel',
        },
        DOMElements: {
            field: '.js-booking-field:single',
            content: '.js-booking-content:single',
            info: '.js-booking-info:single',
            carousel: '.js-booking-carousel:single',
        },
        dataset: {},
        startDateField: 'js-booking-start-date',
        endDateField: 'js-booking-end-date',
        onContentUpdate: () => null,
    };

    private swiperCarousel: any | null = null;
    private maxTime = 0;
    private dates: any[] = [];
    private periods: HTMLElement[] = [];
    private endpoint: string | null = null;
    private startDate: HTMLInputElement | null = null;
    private endDate: HTMLInputElement | null = null;
    private hasBookingAccess = false;
    private canChangeDay = false;
    private bookingKeysHandler: any = null;

    // Bindings
    private readonly $changeHanlder = this.changeHandler.bind(this);
    private readonly $periodHandler = this.periodHandler.bind(this);
    private readonly $rightKeyHandler = this.rightKeyHandler.bind(this);
    private readonly $leftKeyHandler = this.leftKeyHandler.bind(this);
    private readonly $downKeyHandler = this.downKeyHandler.bind(this);
    private readonly $upKeyHandler = this.upKeyHandler.bind(this);
    private readonly $escapeKeyHandler = this.escapeKeyHandler.bind(this);
    private readonly $enterKeyHandler = this.enterKeyHandler.bind(this);
    private readonly $tabKeyHandler = this.tabKeyHandler.bind(this);

    /**
     * Create booking instance.
     * @param {HTMLElement} selector - element to initialize plugin
     * @param {Object} options - Booking options
     */
    public constructor(selector: HTMLElement, options?: IBookingOptions) {
        super(selector, options || {});

        if (!this.created) {
            this.init();
        }
    }

    /**
     * Update periods state and set values for hidden fields.
     * @param {HTMLElement|null} selected - selected period or null if period not choosed
     */
    public updatePeriods(selected: HTMLElement | null = null): void {
        let shouldStop = false;

        // eslint-disable-next-line sonarjs/cognitive-complexity
        this.periods.forEach(period => {
            const timeStart = Number(period.getAttribute('data-start')!);
            const timeEnd = Number(period.getAttribute('data-end')!);

            removeClass(period, 'is-active');
            addClass(period, 'js-disabled');
            setAttributes(period, {
                'tabindex': '-1',
                'aria-pressed': 'false',
            });

            if (!this.dates.length) {
                removeClass(period, 'js-disabled');

                if (hasClass(period, 'is-disabled')) {
                    period.setAttribute('tabindex', '0');
                }

                period.setAttribute('aria-pressed', 'false');
            }

            if (selected && !shouldStop) {
                removeClass(selected, 'js-disabled');
                addClass(selected, 'is-active');
                setAttributes(period, {
                    'tabindex': '0',
                    'aria-pressed': 'true',
                });

                if (timeStart > this.dates[0] && (!this.maxTime || timeStart - this.dates[0] < this.maxTime)) {
                    if (hasClass(period, 'is-disabled')) {
                        shouldStop = true;
                    }

                    removeClass(period, 'js-disabled');

                    if (hasClass(period, 'is-disabled')) {
                        period.setAttribute('tabindex', '0');
                    }
                }
            }

            if (this.dates.length === 2 && timeStart >= this.dates[0] && timeEnd <= this.dates[1]) {
                removeClass(period, 'js-disabled');
                addClass(period, 'is-active');

                if (!hasClass(period, 'is-disabled')) {
                    period.setAttribute('tabindex', '0');
                }

                period.setAttribute('aria-pressed', 'true');
            }
        });

        this.updateValues(selected);
    }

    /**
     * Update hidden fields values.
     * @param {HTMLElement} selected - selected period
     */
    public updateValues(selected: HTMLElement | null = null): void {
        // reset old values
        if (this.startDate && this.endDate) {
            this.startDate.value = '';
            this.endDate.value = '';

            if (this.dates.length && selected) {
                const [start, end] = this.dates;

                this.startDate.value = start;
                this.endDate.value = this.dates.length === 1 ? selected.getAttribute('data-end')! : end;
            }

            this.setActiveButtonsStore(this.startDate.value, this.endDate.value);
        }
    }

    /**
     * Get label parameter from data object and return a11y label value.
     * @param {object} data - object with parameters for period.
     * @param {boolean} time - include time from data to new label.
     * @return {string} - new label value with a11y.
     */
    public createLabel(data, time = false): string {
        const date = new Date(data.start * 1000);
        const year = date.getFullYear();
        const month = `0${date.getMonth() + 1}`.slice(-2);
        const rest = `0${date.getDate()}`.slice(-2);

        let string = '';

        if (l10n.locale.fullDays) {
            string += `${l10n.locale.fullDays[(date.getDay() || 7) - 1]}, `;
        }

        string += `${year}-${month}-${rest}`;

        if (time) {
            string += `, ${data.title}`;
        }

        return string;
    }

    /**
     * Create period element and set settings for it, push it to periods settings.
     * @param {object} data - object with parameters for period.
     * @return {HTMLElement} - HTMLElement for period.
     */
    public addPeriod(data: { [key: string]: any } = {}): HTMLElement {
        // addPeriod
        const period = this.createElement(
            'button',
            {
                class: this.options.classList.period,
                type: 'button',
                tabindex: data.active ? '0' : '-1',
                'aria-pressed': false,
                'aria-label': this.createLabel(data, true),
                'data-start': data.start,
                'data-end': data.end,
            },
            data.title,
        );

        if (!data.active) {
            period.setAttribute('disabled', 'true');
            addClass(period, ['is-disabled', 'js-disabled']);
        }

        this.periods.push(period);
        return period;
    }

    /**
     * Create DOM element.
     * @param {String} tagName - set element tag.
     * @param {Object} attrs - set element attributes.
     * @param {String} html - set element innerHTML.
     * @return {HTMLElement} - return new HTMLElement.
     */
    public createElement(tagName = 'div', attrs = {}, html = ''): HTMLElement {
        const tag = document.createElement(tagName);
        setAttributes(tag, attrs);

        tag.innerHTML = html;

        return tag;
    }

    /**
     * Create instance events.
     */
    protected createEvents(): void {
        const { field } = this.options.DOMElements;

        super.createEvents([
            [field as HTMLElement, 'change', this.$changeHanlder],
            [this.element, 'click', this.$periodHandler],
        ]);

        this.bookingKeysHandler = key({
            right: this.$rightKeyHandler,
            down: this.$downKeyHandler,
            left: this.$leftKeyHandler,
            up: this.$upKeyHandler,
            escape: this.$escapeKeyHandler,
            tab: this.$tabKeyHandler,
            enter: this.$enterKeyHandler,
            space: this.$enterKeyHandler,
        });
    }

    /**
     * Handle interaction on UP key.
     * @param {Event} browserEvent - browser event.
     * @private
     */
    private upKeyHandler(browserEvent): void {
        browserEvent.preventDefault();
        const { target } = browserEvent;
        const { listWrapper, period } = this.options.classList;

        if (target) {
            if (hasClass(target, listWrapper)) {
                const prevElement = getPrevFocusableElement(target, target);

                if (prevElement) {
                    prevElement.focus();
                }
            }

            if (hasClass(target, period)) {
                const prevElement = getPrevFocusableElement(target, target.parentElement);

                if (prevElement) {
                    prevElement.focus();
                }

            }
        }
    }

    /**
     * Handle interaction on DOWN key.
     * @param {Event} browserEvent - browser event.
     * @private
     */
    private downKeyHandler(browserEvent): void {
        browserEvent.preventDefault();
        const { target } = browserEvent;
        const { listWrapper, period } = this.options.classList;

        if (target) {
            if (hasClass(target, listWrapper)) {
                const nextElement = getNextFocusableElement(target, target);

                if (nextElement) {
                    nextElement.focus();
                }
            }

            if (hasClass(target, period)) {
                const nextElement = getNextFocusableElement(target, target.parentElement);

                if (nextElement) {
                    nextElement.focus();
                }
            }
        }

    }

    /**
     * Handle interaction on RIGHT key.
     * @param {Event} event - browser event.
     * @private
     */
    private rightKeyHandler(browserEvent): void {
        browserEvent.preventDefault();
        const { target } = browserEvent;
        const { listWrapper, period } = this.options.classList;
        let nextSlide: any = null;

        if (target && this.canChangeDay) {
            if (hasClass(target, listWrapper) || hasClass(target, period)) {
                nextSlide = target.closest('.swiper-slide').nextElementSibling;
            }

            if (nextSlide) {
                const list = nextSlide.querySelector(`.${listWrapper}`);

                if (nextSlide.getAttribute('aria-hidden') === 'true') {
                    this.swiperCarousel.getCarouselInstance().slideNext(0);
                }

                if (list) {
                    visibleArea({
                        context: nextSlide,
                        callback: () => {
                            list.focus();
                        },
                        area: 0.4,
                    });
                }
            }
        }
    }

    /**
     * Handle interaction on LEFT key.
     * @param {Event} event - browser event.
     * @private
     */
    private leftKeyHandler(browserEvent): void {
        browserEvent.preventDefault();
        const { target } = browserEvent;
        const { listWrapper, period } = this.options.classList;
        let prevSlide: any = null;

        if (target && this.canChangeDay) {
            if (hasClass(target, listWrapper) || hasClass(target, period)) {
                prevSlide = target.closest('.swiper-slide').previousElementSibling;
            }

            if (prevSlide) {
                const list = prevSlide.querySelector(`.${listWrapper}`);

                if (prevSlide.getAttribute('aria-hidden') === 'true') {
                    this.swiperCarousel.getCarouselInstance().slidePrev(0);
                }

                if (list) {
                    list.focus();
                }
            }
        }
    }

    /**
     * Change tabindex for focusable elements inside passed context.
     * @param {HTMLElement} context - element in which we should search.
     * @param {Number} index - tabindex value.
     * @private
     */
    private tabindexHandler(context: HTMLElement, index: string): void {
        const isFocusable = focusable({ context });

        if (isFocusable) {
            if (index === '-1') {
                isFocusable
                    .forEach(element => element.setAttribute('tabindex', index));
            } else {
                isFocusable
                    .filter(element => !hasClass(element, 'js-disabled'))
                    .forEach(element => element.setAttribute('tabindex', index));
            }
        }
    }

    /**
     * Handle interaction on TAB key.
     * @param {Event} event - browser event.
     * @private
     */
    private tabKeyHandler(browserEvent): void {
        const { target } = browserEvent;
        const { period, carousel } = this.options.classList;

        if (target) {
            if (hasClass(target, carousel) && !this.hasBookingAccess) {
                this.tabindexHandler(this.swiperCarousel.getCarouselInstance().el, '-1');
            }

            if (hasClass(target, period) && !this.canChangeDay) {
                const focusableElements = tabbable({
                    context: target.parentElement,
                });

                if (focusableElements && target === focusableElements[focusableElements.length - 1]) {
                    browserEvent.preventDefault();
                    this.hasBookingAccess = false;
                    this.swiperCarousel.getCarouselInstance().el.focus();
                }
            }
        }
    }

    /**
     * Handle interaction on ESCAPE key.
     * @param {Event} event - browser event.
     * @private
     */
    private escapeKeyHandler(browserEvent): void {
        browserEvent.preventDefault();
        const { target } = browserEvent;
        const { listWrapper, period, carousel } = this.options.classList;
        const { field } = this.options.DOMElements as any;
        let redirectTarget: any = null;

        if (target) {
            if (hasClass(target, period)) {
                redirectTarget = target.parentElement;
            }

            if (hasClass(target, carousel) || hasClass(target, listWrapper)) {
                redirectTarget = field;
                this.hasBookingAccess = false;
                this.tabindexHandler(this.swiperCarousel.getCarouselInstance().el, '-1');
            }

            if (redirectTarget) {
                redirectTarget.focus();
            }
        }
    }

    /**
     * Handle interaction on ENTER/SPACE key.
     * @param {Event} event - browser event.
     * @private
     */
    private enterKeyHandler(browserEvent): void {
        const { target } = browserEvent;
        const { carousel } = this.options.classList;
        const { period } = this.options.DOMElements;

        if (target && hasClass(target, carousel)) {
            browserEvent.preventDefault();
            const swiper = this.swiperCarousel.getCarouselInstance();
            this.hasBookingAccess = true;
            this.canChangeDay = [...swiper.el.querySelectorAll(`.${period}.is-active`)].length === 0;
            this.tabindexHandler(swiper.el, '0');
        }
    }

    /**
     * Save booking values to storage.
     * @param args - booking values.
     */
    private setActiveButtonsStore(...args): void {
        sessionStorage.setItem('booking-active-buttons', JSON.stringify(args));
    }

    /**
     * Get booking values from storage.
     */
    private getActiveButtonsStore(): void {
        const activeButtons = JSON.parse(sessionStorage.getItem('booking-active-buttons')!);

        if (!activeButtons) {
            return;
        }

        setTimeout(() => {
            const dateStart = this.element.querySelector<HTMLElement>(`[data-start="${activeButtons[0]}"]`);
            const dateEnd = this.element.querySelector<HTMLElement>(`[data-end="${activeButtons[1]}"]`);

            if (dateStart && dateEnd) {
                dateStart.click();

                if (dateEnd.getAttribute('aria-pressed') === 'false') {
                    dateEnd.click();
                }
            }
        }, 500);
    }

     /**
     * Ged booking data from server.
     * @param {string} uid - record uid.
     * @private
     */
    private async fetchData(uid: string): Promise<any> {
        try {
            const { content } = this.options.DOMElements as any;
            const response = await fetch(this.endpoint!.replace('{id}', uid));
            const json = await response.json();

            this.periods = [];

            if (json) {
                addClass(content, 'is-visible');

                this.maxTime = json.maxTime || 0;
                this.render(json);

                if (this.options.onContentUpdate) {
                    this.options.onContentUpdate();
                }
            }
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Render HTML for Booking widget.
     * @param {JSON} data - booking data, should passed as JSON
     * @private
     */
    private render(data: { [key: string]: string } = {}): void {
        // Render data
        const { info } = this.options.DOMElements as HTMLElementMap;
        const { day, list, listWrapper, period } = this.options.classList;
        const swiper = this.swiperCarousel.getCarouselInstance();

        if (info) {
            info.innerHTML = data.text || '';
        }

        swiper.removeAllSlides();

        Object.keys(data.periods).forEach((date, index) => {
            const periods = data.periods[date];
            const titleLabel = this.createLabel(periods[0]);
            const dayTitle = `${titleLabel.split(',')[0]}<br>${date}`;

            const titleEl = this.createElement(
                'h2',
                {
                    class: day,
                    'aria-label': titleLabel,
                },
                dayTitle
            );

            const listEl = this.createElement(
                'div',
                {
                    class: list,
                },
            );

            const listWrapperEl = this.createElement(
                'div',
                {
                    class: listWrapper,
                    'aria-labelledby': '',
                    tabindex: 0,
                }
            );

            const slide = this.createElement('div', { class: 'swiper-slide' });

            Object.keys(periods).forEach(periodIndex => {
                listWrapperEl.appendChild(this.addPeriod(periods[periodIndex] as any));
            });

            listEl.appendChild(titleEl);
            listEl.appendChild(listWrapperEl);
            slide.appendChild(listEl);

            swiper.addSlide(index, slide);
        });

        const arrows = [...this.element.querySelectorAll('.js-swiper-control')];
        arrows.forEach(arrow => removeClass(arrow as HTMLElement, 'is-hidden'));

        swiper.on('slideChangeTransitionStart', () => {
            const disabledPeriods = [...swiper.el.querySelectorAll(`.${period}[disabled]`)];
            const slides = [...swiper.el.querySelectorAll('.swiper-slide')];

            disabledPeriods.forEach(periodEl => periodEl.setAttribute('tabindex', '-1'));

            slides.forEach(slide => {
                const index = slide.getAttribute('aria-hidden') === 'false' ? '0' : '-1';
                const listEl = slide.querySelector(`.${listWrapper}`);

                if (listEl) {
                    listEl.setAttribute('tabindex', index);
                }
            });
        });

        // I don't know why I write this line, maybe because it should work, but not...
        swiper.init();
        swiper.update();

        // Trick for refreshing arrow attrs
        swiper.slideNext(0);
        swiper.slidePrev(0);
    }

    /**
     * Field change handler.
     * @param browserEvent {event} - browser event.
     */
    private changeHandler(browserEvent): void {
        const { type, target } = browserEvent;
        const { field, content } = this.options.DOMElements as any;
        const { value } = type === 'change' ? target : field.options[field.selectedIndex];

        if (value) {
            this.fetchData(value);
        } else {
            removeClass(content, 'is-visible');
        }
    }


    /**
     * Period handler.
     * @param browserEvent {event} - browser event.
     */
    private periodHandler(browserEvent): void {
        browserEvent.preventDefault();

        const { target } = browserEvent;
        const startDate = target.getAttribute('data-start');
        const endDate = target.getAttribute('data-end');
        const abortScript = !hasClass(target, this.options.classList.period) || hasClass(target, 'js-disabled') || hasClass(target, 'is-disabled');
        this.hasBookingAccess = true;
        this.canChangeDay = false;

        if (abortScript) {
            return;
        }

        if (this.dates.length === 0 || this.dates.length === 2) {
            addClass(target, 'is-active');
            addClass(target.closest('.swiper-wrapper'), 'is-active');
            this.dates = [startDate];
            this.updatePeriods(target);
            return;
        }

        if (this.dates[0] === startDate) {
            this.dates = [];
            this.canChangeDay = true;
            removeClass(target.closest('.swiper-wrapper'), 'is-active');
            this.updatePeriods();
            return;
        }

        this.dates[1] = endDate;
        this.updatePeriods(target);
    }

    /**
     * Initialize instance.
     */
    @OnInit()
    private async initHandler(): Promise<void> {
        const { field, carousel } = this.options.DOMElements as any;

        if (!field && !carousel) {
            return;
        }

        const { value } = field.options[field.selectedIndex];
        const { default: Carousel } = await import('@core/core-components/carousel');
        this.swiperCarousel = new Carousel(carousel as HTMLElement);
        this.endpoint = field.getAttribute('data-action');
        this.startDate = this.element.querySelector(`.${this.options.startDateField}`);
        this.endDate = this.element.querySelector(`.${this.options.endDateField}`);

        if (!this.startDate && !this.endDate && !this.endpoint) {
            return;
        }

        if (value) {
            this.fetchData(value);
            this.getActiveButtonsStore();
        }
    }

    /**
     * Remove ally handler.
     */
    @OnDestroy()
    private destroyHandler(): void {
        this.bookingKeysHandler.disengage();
    }
}

const BookingFactory = StratisFactoryMixin<typeof Booking, Booking, IBookingOptions>(Booking);
export default BookingFactory;
