// Folder name to export
const folder = 'stratis_site';
// Folder name to get the files
const srcFolder = './dist';
// Root folder for destination path
const distRoot = '../../www';
// Path to assets
const pathToAssets = '/public/typo3conf/ext/[folder]/Resources/Public';
// Browser paths to js files (Needs for dynamic import)
const jsBrowserPath = '/typo3conf/ext/[folder]/Resources/Public/JavaScript/';
// Destination path
// use placeholder [folder] to insert folder variable which we
// declare above.
const distFolder = distRoot + pathToAssets;

// Export configuration
const exportConfig = {
    // Export type, used for overwriting global paths for assets,
    // etc (use CMS name for it).
    type: 'typo3',

    // Source folder
    srcFolder: srcFolder,

    // Project folder in which we should export
    folder: folder,

    //Path to assets
    pathToAssets: pathToAssets,

    jsBrowserPath: jsBrowserPath,

    // Main project assets
    // Here described main export configuration
    // Run it with gulp export command
    assets: [
        {
            type: 'css',
            src: [
                `${srcFolder}/css/**/*.css`,
                `!${srcFolder}/css/styleguide.css`,
                `!${srcFolder}/css/map.css`,
            ],
            dist: `${distFolder}/Css/`
        },
        {
            type: 'js',
            src: [
                `${srcFolder}/js/**/*`,
                `!${srcFolder}/js/map.bundle.js`,
                `!${srcFolder}/js/localiser.bundle.js`,
                `!${srcFolder}/js/booking.bundle.js`,
                `!${srcFolder}/js/advertising.js`,
                `!${srcFolder}/js/tarteaucitron.js`,
                `!${srcFolder}/js/tarteaucitron.services.js`,
                `!${srcFolder}/js/lang/tarteaucitron.*.js`,
                `!${srcFolder}/js/generate-pdf.bundle.js`
            ],
            dist: `${distFolder}/JavaScript/`
        },
        {
            type: 'images',
            src: `${srcFolder}/images/**/*`,
            dist: `${distFolder}/Images/`
        },
        {
            type: 'fonts',
            src: `${srcFolder}/fonts/**/*`,
            dist: `${distFolder}/Fonts/`
        },
        {
            type: 'file',
            src: `${srcFolder}/media/**/*`,
            dist: `${distFolder}/Media/`
        },
        {
            type: 'file',
            src: `${srcFolder}/sw.js`,
            dist: `${distFolder}/`
        },
    ],

    // DON'T USE THIS NAME FOR NAMED EXPORT
    // Sitefactory configuration
    sitefactory: {
        src: 'src/scss/sf-main.scss',
        dist: '../../www/public/typo3conf/ext/stratis_site/Resources/Private/Css/'
    },

    // Named export
    // Booking export
    // gulp export --name=booking
    booking: [
        {
            type: 'js',
            src: `${srcFolder}/js/booking.bundle.js`,
            dist: `${distFolder}/JavaScript/`
        }
    ],

    // Map export
    // gulp export --name=map
    map: [
        {
            type: 'js',
            src: [
                `${srcFolder}/js/map.bundle.js`,
                `${srcFolder}/js/localiser.bundle.js`,
            ],
            dist: `${distFolder}/JavaScript/`
        },
        {
            type: 'css',
            src: `${srcFolder}/css/map.css`,
            dist: `${distFolder}/Css/`
        }
    ],

    // Maintenance page export
    // gulp export --name=maintenance
    maintenance: [
        {
            type: 'file',
            src: `${srcFolder}/maintenance.html`,
            dist: `${distRoot}/maintenance/`
        }
    ],

    // Cookies ext export
    // gulp export --name=cookies
    cookies: [
        {
            type: 'css',
            src: `${srcFolder}/css/stratis-tarteaucitron-v3.css`,
            dist: `${distRoot}/public/typo3conf/ext/stratis_common/Resources/Public/Css/Library/Tarteaucitron/`
        },
        {
            type: 'js',
            src: [
                `${srcFolder}/js/tarteaucitron.js`,
                `${srcFolder}/js/tarteaucitron.services.js`,
                `${srcFolder}/js/stratis-tarteaucitron.bundle.js`
            ],
            dist: `${distRoot}/public/typo3conf/ext/stratis_common/Resources/Public/JavaScript/Library/Tarteaucitron/`
        },
        {
            type: 'js',
            src: `${srcFolder}/js/lang/tarteaucitron.*.js`,
            dist: `${distRoot}/public/typo3conf/ext/stratis_common/Resources/Public/JavaScript/Library/Tarteaucitron/lang/`
        }
    ]
};

export default exportConfig;
