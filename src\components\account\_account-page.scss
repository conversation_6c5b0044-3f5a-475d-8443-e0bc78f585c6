body.is-account-page {
    background: linear-gradient(to bottom, $color-white 540px, var(--color-1--2) 540px);

    @include breakpoint(medium down) {
        background: linear-gradient(to bottom, $color-white 740px, var(--color-1--2) 740px);
    }

    @include breakpoint(small down) {
        background: linear-gradient(to bottom, $color-white 820px, var(--color-1--2) 820px);
    }

    .site-content__wrapper {
        padding: 0 40px;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    .site-content__main {
        background-color: $color-white;
        box-shadow: 0 0 20px rgba($color-black, 0.1);
        padding: 50px;
        position: relative;

        @include breakpoint(small down) {
            padding: 40px 20px;
        }
    }
}

body.is-main-account-page {
    background: linear-gradient(to bottom, $color-3--1 1626px, $color-white 1626px);

    @include breakpoint(medium down) {
        background: linear-gradient(to bottom, $color-3--1 1939px, $color-white 1939px);
    }

    @include breakpoint(small down) {
        background: linear-gradient(to bottom, $color-3--1 1280px, $color-white 1280px);
    }

    .site-content__wrapper {
        padding: 0 40px;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }
}
