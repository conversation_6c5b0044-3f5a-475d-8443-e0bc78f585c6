const distFolder = 'dist';
const srcFolder = 'src';

// Switch notifications
const enableNotifications = false;

const config = {
    // Production mode
    production: false,

    use: {
        iconFont: false,
        iconSprite: false,
        stylesCritical: true,
    },

    // Remove dist folder
    clean: {
        src: distFolder,
    },

    // Export type (env variable for scss)
    // $type: 'your-value', default value is 'development'
    type: "development",

    // Templates configuration
    views: {
        root: srcFolder,
        src: [
            `${srcFolder}/views/pages/*.njk`,
        ],
        dist: distFolder,
        listen: [
            `${srcFolder}/views/**/*.njk`,
            `${srcFolder}/components/**/*.njk`,
        ],
    },

    // Styles configuration
    styles: {
        src: [
            `${srcFolder}/scss/**/*.scss`,
            `${srcFolder}/components/**/*.scss`,
            `!${srcFolder}/scss/**/sf-*.scss`,
        ],
        dist: `${distFolder}/css`,
        optimization: {
            src: `${distFolder}/css/*.css`,
            dist: `${distFolder}/css/`
        },
        critical: {
            cleanMaps: `${distFolder}/css/main*.css.map`,
            src: [
                `${distFolder}/css/main.css`,
                `!${distFolder}/css/main-critical.css`,
                `!${distFolder}/css/main-rest.css`
            ],
            dist: `${distFolder}/css`
        },
        listen: [`${srcFolder}/**/**/*.scss`],
    },

    // JavaScript configuration
    scripts: {
        debug: false,

        // Browser paths to js files (Needs for dynamic import)
        browserPath: '/js/',

        src: [
            // `${srcFolder}/components/**/*.{ts,js}`,
            // `${srcFolder}/js/**/*.{ts,js}`
            `${srcFolder}/js/*.{ts,js}`,
        ],
        dist: `${distFolder}/js/`,
        listen: [
            `${srcFolder}/components/**/*.{ts,js,json}`,
            `${srcFolder}/js/**/*.{ts,js,json,gpx}`,
        ],
    },

    // Images configuration
    images: {
        srcMain: `${srcFolder}/images/`,
        srcSVG: `${srcFolder}/images/**/*.svg`,
        src: `${srcFolder}/images/**/*`,
        dist: `${distFolder}/images/`,
        listen: [`${srcFolder}/images/**/*`],
    },

    // Fonts configuration
    fonts: {
        src: `${srcFolder}/fonts/**/*`,
        dist: `${distFolder}/fonts/`,
        listen: [`${srcFolder}/fonts/**/*`],
    },

    // Custom icons font
    iconsFont: {
        src: `${srcFolder}/icons/font/svg/*.svg`,
        dist: `${distFolder}/fonts/project-icons`,
        name: "project-icons",
        templates: {
            html: `${srcFolder}/icons/font/templates/icons.html`,
            css: `${srcFolder}/icons/font/templates/icons.css`,
        },
        target: {
            html: "../../icons.html",
            css: "../../../src/scss/_icons.scss",
        },
        fontPath: "#{$assets-project-icons}",
    },

    // Svg sprite
    sprite: {
        src: `${srcFolder}/icons/font/svg/*.svg`,
        dist: `${distFolder}/icons/`,
        name: "sprite",
        template: `${srcFolder}/icons/sprite/templates/sprite.html`,
    },

    // Other files
    files: [
        {
            src: [
                `${srcFolder}/js/**/*.{json,gpx}`,
                `${srcFolder}/components/**/*.json`,
                `${srcFolder}/js/cookies/*.js`,
                "!node_modules/tarteaucitronjs/advertising.js",
            ],
            dist: `${distFolder}/js/`,
        },
        { src: [`${srcFolder}/media/**/*`], dist: `${distFolder}/media/` },
        { src: [`${srcFolder}/js/sw.js`], dist: `${distFolder}/` },
        { src: ['node_modules/tarteaucitronjs/lang/*.js'], dist: `${distFolder}/js/lang/` },
    ],
};

export default config;
export {
    srcFolder,
    distFolder,
    enableNotifications
};
