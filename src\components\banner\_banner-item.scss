.banner-item {
    $this: &;

    @extend %link-block-context;
    align-items: stretch;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-inline: auto;
    position: relative;
    width: 996px;
    z-index: 1;

    @include breakpoint(medium down) {
        width: 644px;
    }

    @include breakpoint(small down) {
        flex-direction: column-reverse;
        width: 100%;
    }

    @include breakpoint(small down, true) {
        flex-direction: column-reverse;

        &.is-reverse {
            flex-direction: column;
        }
    }

    &__image {
        @include size(447px, 296px);
        margin-bottom: -36px;
        margin-left: 37px;
        margin-top: -47px;
        
        @include breakpoint(medium down) {
            @include size(322px, 198px);
            margin-bottom: -32px;
            margin-left: 0;
            margin-top: -29px;
        }

        @include breakpoint(small down) {
            margin-bottom: -35px;
            width: 100%;
        }

        @include breakpoint(small down, true) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100% !important);
        }
    }

    &__content {
        @include size(50%, null);
        align-items: center;
        display: flex;
        padding: 26px 100px 40px 69px;

        @include breakpoint(medium down) {
            align-items: flex-start;
            padding: 27px 60px 21px 30px;
        }

        @include breakpoint(small down) {
            padding: 34px 50px 60px 34px;
            width: 100%;
            
            &::after {
                @include size(100%, 30px);
                @include absolute(139px, 0, null, 0);
                background-color: var(--color-2--1);
                content: '';
                mix-blend-mode: multiply;
                z-index: 1;
            }
        }

        .is-inverted & {
            color: $color-white;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        @include absolute(null, 405px, 53px, null);
        font-size: 2.5rem;
        font-weight: var(--fw-normal);

        @include breakpoint(medium down) {
            bottom: 42px;
            font-size: 1.6rem;
            right: 276px;
        }

        @include breakpoint(small down) {
            @include absolute(null, 24px, 43px, 34px);
        }
    }

    &__title {
        @include font(var(--typo-1), 2.6rem, var(--fw-bold));
        display: inline;
        line-height: 30px;
        
        @include breakpoint(medium down) {
            font-size: 2rem;
            line-height: 24px;
        }

        .underline {
            .is-inverted & {
                @include multiline-underline();
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__title-text {
        display: flex;
        flex-wrap: wrap;

        span {
            margin-right: 7px;

            &:first-of-type {
                width: 100%;
            }

            &:last-of-type {
                margin-right: 0;
            }
        }
    }

    &.is-reverse {
        flex-direction: row;

        @include breakpoint(small down) {
            flex-direction: column;
        }

        @include fa-icon-style(false) {
            left: 70px;

            @include breakpoint(medium down) {
                left: 30px;
            }

            @include breakpoint(small down) {
                bottom: 173px;
                left: 35px;
                right: 24px;
            }

            @include breakpoint(xsmall down) {
                bottom: 170px;
            }
        }

        #{$this} {
            &__content {
                @include breakpoint(small down) {
                    padding: 24px 50px 58px 34px;

                    &::after {
                        bottom: 133px;
                        top: auto;
                    }
                }
            }

            &__image {
                margin-right: 37px;

                @include breakpoint(medium down) {
                    margin-right: 0;
                }
            }
        }

        &.is-inverted {
            #{$this}__content {
                @include breakpoint(small down) {
                    &::after {
                        bottom: 133px;
                    }
                }
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            align-items: center;
            flex-direction: column-reverse;
            position: relative;
            width: 320px;

            #{$this}__image {
                @include size(320px, 198px);
                margin-inline: 0;
                margin-top: 0;
            }

            #{$this}__content {
                padding: 33px 30px 39px 25px;
                width: 100%;

                &::after {
                    @include size(100%, 30px);
                    @include absolute(168px, 0, null, 0);
                    background-color: var(--color-2--1);
                    content: '';
                    mix-blend-mode: multiply;
                    z-index: 1;
                }
            }

            #{$this}__title {
                font-size: 2.4rem;
                line-height: calc(28 / 24);
            }

            @include fa-icon-style(false) {
                bottom: 21px;
                font-size: 1.9rem;
                right: auto;
            }

            &.is-reverse {
                flex-direction: column;

                #{$this}__content {
                    padding-bottom: 42px;

                    &::after {
                        top: 84px;
                    }
                }

                @include fa-icon-style(false) {
                    bottom: 138px;
                    left: 27px;
                    right: auto;
                    z-index: 1;
                }
            }

            &.is-reverse,
            &.is-inverted.is-reverse {
                #{$this}__image {
                    margin-top: -40px;
                }

                #{$this}__content {
                    padding: 10px 30px 66px 25px;

                    &::after {
                        bottom: 132px;
                        top: auto;
                    }
                }

                @include fa-icon-style(false) {
                    bottom: 170px;
                    right: auto;
                }
            }
        }
    }

    .page-content & {
        @include breakpoint(medium down) {
            margin-top: 40px;
        }
    }
}
