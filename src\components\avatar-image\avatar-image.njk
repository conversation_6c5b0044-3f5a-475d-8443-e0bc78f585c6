{% from 'views/core-components/image.njk' import Image %}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    AvatarImage template.
    @param {string} className - class modifier
    @param {string} size - image size.
#}
{%- macro AvatarImage(className = '', size = '128x128') -%}
    {{ Image({
        className: 'avatar-image ' + className,
        sizes: [size]
    }) }}
{%- endmacro -%}

{#
    AvatarImageSG template.
    Styleguide template.
#}
{%- macro AvatarImageSG() -%}
    {% call SG.Section('avatar-image') %}
        <h2 class="styleguide-section__title">Avatar</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-sm-12">
                    <h3 class="styleguide-section__subtitle">Default avatar</h3>
                    <div class="flex-list bottom-xs has-mb-3">
                        {{ AvatarImage('has-mr-1') }}
                        {{ AvatarImage(className = 'is-96x96 has-mr-1', size = '96x96') }}
                        {{ AvatarImage(className = 'is-64x64 has-mr-1', size = '64x64') }}
                        {{ AvatarImage(className = 'is-48x48 has-mr-1', size = '48x48') }}
                        {{ AvatarImage(className = 'is-32x32 has-mr-1', size = '32x32') }}
                        {{ AvatarImage(className = 'is-16x16', size = '16x16') }}
                    </div>
                    <h3 class="styleguide-section__subtitle">Rounded avatar</h3>
                    <div class="has-mb-3">
                        <div class="flex-list bottom-xs has-mb-3">
                            {{ AvatarImage('is-rounded has-mr-1') }}
                            {{ AvatarImage(className = 'is-rounded is-96x96 has-mr-1', size = '96x96') }}
                            {{ AvatarImage(className = 'is-rounded is-64x64 has-mr-1', size = '64x64') }}
                            {{ AvatarImage(className = 'is-rounded is-48x48 has-mr-1', size = '48x48') }}
                            {{ AvatarImage(className = 'is-rounded is-32x32 has-mr-1', size = '32x32') }}
                            {{ AvatarImage(className = 'is-rounded is-16x16', size = '16x16') }}
                        </div>
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}



