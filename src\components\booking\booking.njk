{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}

{%- macro BookingCarousel(settings = {}) -%}
    <fieldset class="booking js-booking">
        <legend class="ghost">Réservation</legend>
        <div class="form__field-wrapper">
            <label for="booking-field" class="form__label">Démarches :<em class="required" aria-hidden="true">(obligatorie)</em></label>
            <select class="form__field js-booking-field" name="booking_field" id="booking-field" data-action="{{ Helpers.path.js }}/data/booking-{id}.json" required>
                <option value="">-- Sélectionner --</option>
                <option value="1" selected>Obtenir un passeport</option>
                <option value="2">Délivrance du permis de conduire</option>
                <option value="3">Photos pour passeports étrangers</option>
            </select>
            <div class="booking__content js-booking-content">
                <div class="booking__info js-booking-info rte"></div>
                {% call CarouselWrapper( settings = {
                    wrapperClassName: 'booking-carousel',
                    jsClassName: 'js-booking-carousel',
                    itemsToShow: '5, 3, 1',
                    actions: false
                }) %}{% endcall %}
            </div>

            <input type="hidden" class="js-booking-start-date" value="">
            <input type="hidden" class="js-booking-end-date" value="">
        </div>
    </fieldset>
{%- endmacro -%}
