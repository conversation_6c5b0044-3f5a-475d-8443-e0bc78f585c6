@use 'sass:math';

/* stylelint-disable */
@import 'fontawesome/functions';
@import 'fontawesome/variables';
/* stylelint-enable */

@import './helpers';

// HERO
@import '../components/hero/hero';
@import '../components/hero/hero-carousel';
@import '../components/hero/hero-item';
@import '../components/hero/hero-search';
@import '../components/hero/hero-type-2';
@import '../components/hero/hero-type-3';
@import '../components/hero/hero-type-4-and-7';
@import '../components/hero/hero-type-5';
@import '../components/hero/hero-type-6';
@import '../components/hero/hero-type-7';
@import '../components/hero/hero-slider';
@import '../components/hero/slider-item';

// QUICKLINKS
@import '../components/quicklinks/quicklinks-home';
@import '../components/quicklinks/quicklinks-carousel';
@import '../components/quicklinks/quicklinks-item';
@import '../components/quicklinks/quicklinks-info';

// NEXT COUNCIL
@import '../components/next-council/next-council';

//NUMBERS
@import '../components/numbers/numbers';
@import '../components/numbers/numbers-item';

// Presentation
@import '../components/presentation/presentation';
@import '../components/presentation/presentation-item';

// SOCIAL WALL
@import '../components/social-wall/social-wall';
@import '../components/social-wall/social-wall-item';

// LOCATION
@import '../components/location/location';
@import '../components/location/location-home';
@import '../components/location/location-type-2';
@import '../components/location/location-type-3';

// SEARCH SERVICES
@import '../components/search/search-item';
@import '../components/search-options/search-options';
@import '../components/search-services/search-services';

// ACCOUNT COMPONENTS
// Don't remove this import on Drupal projects
@import '../components/account/account-message';
