.presentation-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    max-width: 100%;
    width: 100%;

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    &__picture-wrap {
        @include size(482px);
        @include absolute(0, null, null, null);
        flex-shrink: 0;

        @include breakpoint(medium down) {
            width: 200px;
        }
    }

    &__picture {
        @include size(100%, auto);

        img {
            @include object-fit();
            @include size(100% !important);
            display: block;
        }
    }

    &__content {
        flex-grow: 1;
        padding-left: 80px;
        width: 1%;

        @include breakpoint(medium down) {
            padding-left: 34px;
        }

        @include breakpoint(small down) {
            padding: 38px 0 0 0;
            text-align: center;
            width: 100%;
        }
    }

    &__quote {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: var(--color-1--2);
        line-height: 25px;
        margin: 0 0 95px;
        padding-bottom: 0;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        @include breakpoint(small down) {
            margin-bottom: 24px;
        }
    }

    .presentation-home & {
        &__picture-wrap {
            @include size(482px);
            
            @include breakpoint(medium down) {
                @include size(384px);
                top: 66px;
            }
            
            @include breakpoint(small down) {
                @include size(216px);
                margin-right: auto;
                position: relative;
                top: 21px;
            }

            &::after {
                @include size(147px);
                right: -110px;
                top: -6px;

                @include breakpoint(medium down) {
                    @include size(85px);
                    right: -25px;
                    top: -9px;
                }

                @include breakpoint(small down) {
                    bottom: -32px;
                    right: 50%;
                    top: auto;
                    transform: translateX(-50%);
                }
            }
        }

        &__content {
            display: flex;
            flex: none;
            flex-grow: 0;
            margin-left: 484px;
            margin-top: 52px;
            max-width: 444px;
            padding: 0 0 0 64px;
            width: fit-content;

            @include breakpoint(medium down) {
                margin-left: 394px;
                margin-top: 40px;
                max-width: 311px;
                padding: 0 0 0 35px;
            }

            @include breakpoint(small down) {
                margin-left: 0;
                margin-top: 0;
                max-width: 100%;
                padding: 38px 22px 0 20px;
                text-align: left;
            }
        }

        &__quote {
            font-size: 1.6rem;
        }
    }
}
