.practical-information {
    display: flex;

    @include breakpoint(medium down) {
        flex-direction: column;
    }

    &__infos {
        background-color: $color-3--1;
        max-width: 692px;
        padding: 60px 135px 60px 0;
        position: relative;
        width: 58%;
        
        @include breakpoint(medium down) {
            max-width: 100%;
            padding: 40px;
            width: 100%;
        }
        
        @include breakpoint(small down) {
            padding: 20px;
        }

        &::before {
            @include size(100vw, 100%);
            @include absolute(0, null, 0, 0);
            background-color: $color-3--1;
            content: '';
            transform: translate(-50%);
            z-index: -1;

            @include breakpoint(medium down) {
                width: 1000vw;
            }
        }
    }
    
    &__title {
        @include font(var(--typo-1), 4.4rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 46px;
        margin-bottom: 37px;
        
        @include breakpoint(small down) {
            font-size: 2.5rem;
            line-height: 25px;
            margin-bottom: 20px;
        }
    }
    
    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        line-height: 30px;

        @include breakpoint(small down) {
            font-size: 1.6rem;
            line-height: 25px;
        }

        p {
            margin-bottom: 10px;
        }
    }
    
    &__items {
        background-color: var(--color-1--1);
        padding: 54px 10px 47px 72px;
        position: relative;
        width: 42%;
        
        @include breakpoint(medium down) {
            padding: 40px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 20px;
        }

        &::before {
            @include size(100vw, 100%);
            @include absolute(0, null, 0, 0);
            background-color: var(--color-1--1);
            content: '';
            transform: translate(0);
    
            @include breakpoint(medium down) {
                transform: translate(-50%);
                width: 1000vw;
            }
        }
    }

    &__item {
        margin-bottom: 13px;
        padding-left: 31px;
        position: relative;
    }

    &__item-svg {
        @include size(20px);
        @include absolute(0, null, null, 0);
        fill: $color-white;
        font-size: 2rem;
        line-height: 25px;
    }

    &__item-title {
        @include font(var(--typo-1), 1.5rem, var(--fw-bold));
        color: $color-white;
        line-height: 22px;
    }

    &__item-subtitle {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-white;
        line-height: 18px;
    }
}
