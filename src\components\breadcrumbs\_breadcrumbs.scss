// @name Breadcrumb

.breadcrumbs {
    $this: &;

    display: block;

    & &__list {
        @extend %clear-list-styles;
        display: flex;
        flex-wrap: wrap;
        position: relative;

        @include breakpoint(medium down) {
            padding-left: 16px;
        }
    }

    &__item {
        --bc-item-display: flex;
        --bc-item-color: #{$color-3--7};

        @include font(var(--typo-1), 1.2rem);
        align-items: center;
        color: var(--bc-item-color);
        display: var(--bc-item-display);
        font-weight: var(--fw-medium);

        @include breakpoint(medium down) {
            &:not(.is-visible) {
                --bc-item-display: none;
            }

            &:first-child,
            &.is-toggle,
            &:last-child {
                --bc-item-display: flex;
            }
        }

        @include breakpoint(small down) {
            font-size: 1.1rem;
        }

        &:first-child {
            font-size: 1.6rem;

            a {
                color: var(--bc-item-color);

                @include on-event {
                    --bc-item-color: var(--color-1--1);
                }

                @include fa-icon-style {
                    @include trs;
                    font-weight: var(--fw-normal);
                }
            }

            @include breakpoint(medium down) {
                @include absolute(4px, null, null, 0);
            }

            @include breakpoint(small down) {
                top: 6px;
            }
        }

        &.is-toggle {
            @include breakpoint(large only) {
                display: none;
            }
        }

        &.is-hidden {
            --bc-item-display: none;
        }

        &:last-child {
            --bc-item-color: var(--color-1--1);
        }

        + #{$this}__item {
            &::before {
                @include size(10px, 32px);
                background: {
                    image: inline-svg('<svg fill="#{$color-3--3}" xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512"><polygon points="203.02 512.06 180.62 500.86 303.03 256.05 180.62 11.26 203.02 0.06 331 256.05 203.02 512.06"/></svg>');
                    position: center;
                    repeat: no-repeat;
                    size: 20px;
                }
                content: '';
                margin: 0 11px;

                @include breakpoint(small down) {
                    background-size: 18px;
                }
            }
        }
    }

    &__items-toggle {
        background-color: transparent;
        border: 0;
        color: $color-3--4 !important;
        cursor: pointer;
        padding: 0;
    }

    a {
        @include focus-outline($offset: 2px);
        text-decoration: none;

        @include on-event {
            text-decoration: underline;
        }
    }
}

.breadcrumbs-nav {
    &__toggle {
        @include focus-outline($offset: 2px);
        background-color: transparent;
        border: 0;
        color: var(--color-1--1);
        display: flex;
        flex-direction: row-reverse;
        font-weight: var(--fw-bold);
        padding: 0;
        text-align: left;

        @include on-event {
            text-decoration: underline;
        }

        > * {
            pointer-events: none;
        }
    }

    &__toggle-icon {
        @include icon-after($fa-var-angle-down);

        &::after {
            @include trs;
            font-size: 1.3rem;
            margin-left: 5px;
        }

        .is-open & {
            &::after {
                transform: rotateX(180deg);
            }
        }
    }

    &__block[class] {
        background-color: $color-white;
        border-radius: 0 5px 5px 5px;
        min-width: 250px;
        top: 110%;

        @include breakpoint(small down) {
            min-width: 220px;
        }
    }

    &__menu-links[class] {
        max-height: 145px;
        overflow-y: auto;
        padding: 6px 4px;
    }

    &__item {
        line-height: 1.15;
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__link {
        color: $color-3--4;
        font-weight: var(--fw-normal);
    }
}
