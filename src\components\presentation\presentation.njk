{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'components/key-box/key-box.njk' import  KeyBoxItem -%}

{#
    PresentationHome template.
    Template for presentation on home page.
    @param {string} titleText - section title
    @param {boolean} moreButton - insert more link
#}
{% macro PresentationHome(titleText = 'Bienvenue à Clairvivre', subtitleText = 'Surtitre', imageSizes = [
    '216x216?767', '384x384?1279', '482x482'
], quote = 'Situé dans le Périgord vert, au centre du triangle Limoges, Brive et Périgueux, avec des antennes à Boulazac et à Terrasson, l’Établissement Public Départemental de Clairvivre accueille et accompagne des personnes adultes en situation de handicap ou de vulnérabilité.') %}
    {% call Section(className = 'presentation-home', container = 'presentation-home__container') %}
    <div class="section__content">
        <div class="presentation-item">
            <div class="presentation-home__title">
                <p class="subtitle-text">{{ subtitleText }}</p>
                {{ Title.TitlePrimary(text = titleText) }}
            </div>
            <div class="presentation-item__picture-wrap">
                {{ Image({sizes: imageSizes, className: 'presentation-item__picture', serviceID: range(50) | random, alt: ''}) }}
            </div>
            <div class="presentation-item__content">
                {% if quote %}
                    <blockquote class="presentation-item__quote">{{ quote }}</blockquote>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="section__more-links">
        {{ Link(href = "#", text = 'En savoir plus', className = 'btn is-sm-small is-primary', icon = false) }}
    </div>
    {% endcall %}
{% endmacro %}

{% macro PresentationKeyNumberHome(
    items = [{
        icon : 'icons/ico-_1', 
        text : 'Logements',
        title : '220'
    },
    {
        icon : 'icons/ico-_2', 
        text : 'agents',
        title : '350'
    },
    {
        icon : 'icons/ico-_3', 
        text : 'résidents',
        title : '180'
    }]
) %}

    {% call Section(className = 'presentation-keynumber-home', container = 'presentation-keynumber-home__container') %}
    {{ PresentationHome() }}
    <ul class="key-box-list">
        {%- for item in items -%}
            <li class="list__item">
                <a href="#" class="key-box-item__link">
                    {{ KeyBoxItem(icon = item.icon, title=item.title, unit="", description = item.text) }}
                </a>
            </li>
        {%- endfor -%}
    </ul>
    {% endcall %}
{% endmacro %}