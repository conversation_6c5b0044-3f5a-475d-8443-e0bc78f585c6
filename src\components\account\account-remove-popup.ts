import { isFe } from '@core/utils/env.utils';
import { Fancybox } from '@fancyapps/ui';

// eslint-disable-next-line sonarjs/cognitive-complexity
export default function accountRecordDeleteConfirmation(): void {
    const selector: HTMLElement | null = document.querySelector('#account-remove-popup');
    const trigger: HTMLElement | null = document.querySelector('.js-account-remove-popup');
    let buttonAgree: HTMLElement | null = null;
    let buttonRemove: HTMLAnchorElement | null = null;

    const sendRequest = async (url: string, redirectUrl: string | undefined): Promise<void> => {
        try {
            const response = await fetch(url, {
                method: isFe() ? 'GET' : 'POST',
            });
            if (response.status >= 200 && response.status < 300) {
                Fancybox.close();

                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }

                if (buttonRemove) {
                    const row: HTMLElement = buttonRemove.parentElement!.parentElement!;
                    const tbody: any = row.parentElement;
                    const tableResponsive: HTMLElement | null = buttonRemove.closest('.table-responsive');
                    row.remove();

                    if (tbody && tbody.rows && tbody.rows.length === 0 && tableResponsive) {
                        tableResponsive.remove();
                    }
                }
            }
        } catch (e) {
            console.warn(e);
        }
    };

    const agreeHandler = (browserEvent): void => {
        browserEvent.preventDefault();
        const { target } = browserEvent;

        if (target.hasAttribute('data-action')) {
            const url: string = target.getAttribute('data-action');
            const redirectUrl: string | undefined = target.getAttribute('data-redirect');

            sendRequest(url, redirectUrl);
        }
    };

    const handleButtonAgree = (selector, buttonRemove): void => {
        if (selector && buttonRemove) {
            const url: string = buttonRemove.getAttribute('data-href')!;
            const redirectUrl: string = buttonRemove.getAttribute('data-redirect')!;
            buttonAgree = selector.querySelector('.account-message__remove');
            buttonAgree?.setAttribute('data-action', url);
            if (redirectUrl) {
                buttonAgree?.setAttribute('data-redirect', redirectUrl);
            }
            buttonAgree?.addEventListener('click', agreeHandler);
        }
    };

    if (trigger) {
        trigger.addEventListener('click', () => {
            setTimeout(() => {
                const fancybox = Fancybox.getInstance();
                fancybox!.on('reveal', (fancybox, slide) => {
                    buttonRemove = slide.triggerEl as HTMLAnchorElement;
                    if (buttonRemove) {
                        handleButtonAgree(selector, buttonRemove);
                    }
                });
            });
        });
    }
}
