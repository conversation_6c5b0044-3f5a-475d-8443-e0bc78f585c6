@use 'sass:math';

@import './helpers';

/* stylelint-disable */
@import 'fontawesome/brands';
@import 'fontawesome/regular';
// @import 'fontawesome/solid';
// @import 'fontawesome/light';
@import 'fontawesome/fontawesome';
/* stylelint-enable */

@import 'choices.js/src/styles/choices';
@import 'swiper/swiper';
@import url('@fancyapps/ui/dist/fancybox/fancybox.css');
@import url('tarteaucitronjs/css/tarteaucitron.css');
// @import 'icons';

@import 'base/typography';
@import 'base/rte';

// import core helpers styles
@import 'core-components/title';
@import 'core-components/teaser';
@import 'core-components/theme';
@import 'core-components/item-title';
@import 'core-components/item-teaser';
@import 'core-components/dropdown';
@import 'core-components/fixed-element';
@import 'core-components/click-and-roll';
@import 'core-components/pager';
@import 'core-components/message';
@import 'core-components/tag';
@import 'core-components/publication';
@import 'core-components/tabs';
@import 'core-components/gallery';
@import 'core-components/ce-gallery';
@import 'core-components/carousel';
@import 'core-components/list';
@import 'core-components/form';
@import 'core-components/form-label';
@import 'core-components/form-checkbox-radio';
@import 'core-components/form-multiselect';
@import 'core-components/form-autocomplete';
@import 'core-components/form-file-list';
@import 'core-components/form-avatar';
@import 'core-components/form-choices';
@import 'core-components/password-rules';
@import 'core-components/password-complexity';
@import 'core-components/video';
@import 'core-components/secondary-elements';
@import 'core-components/accesstooltip';
@import 'core-components/cookies';
@import 'core-components/tarteraucitron';
@import 'core-components/svg';

// key-box
@import '../components/key-box/key-box';

// custom-map
@import '../components/custom-map/custom-map';

// BASE COMPONENTS
@import '../components/flash-info/flash-info';
@import '../components/flash-info/flash-info-item';
@import '../components/flash-info/flash-info-popup';
@import '../components/avatar-image/avatar-image';
@import '../components/popup/popup';

// SEARCH COMPONENTS
@import '../components/search/search';
@import '../components/search/search-item';
@import '../components/search/search-tags';
@import '../components/search-options/search-options';

// NEWS COMPONENTS
@import '../components/news/news';
@import '../components/news/news-focus';
@import '../components/news/news-list';
@import '../components/news/news-item';
@import '../components/news/shortnews';
@import '../components/news/shortnews-item';

// Projects
@import '../components/projects/projects';
@import '../components/projects/projects-vertical';
@import '../components/projects/project-item';
@import '../components/projects/project-focus';

// PUBLICATIONS
@import '../components/publications/publications';
@import '../components/publications/publications-home-item';
@import '../components/publications/publications-content-item';
@import '../components/publications/publications-list-item';

// EVENTS
@import '../components/events/events';
@import '../components/events/event-item';
@import '../components/events/event-focus';
@import '../components/events/event-banner';
@import '../components/events/eventlinks-list';
@import '../components/events/eventlinks-item';

// DISCOVER
@import '../components/discover/discover';
@import '../components/discover/discover-item';

// Albums
@import '../components/albums/albums';
@import '../components/albums/album-item';

// REVIEWS
@import '../components/reviews/reviews';
@import '../components/reviews/reviews-item';

// WEATHER
@import '../components/weather/weather';
@import '../components/weather/weather-item';

// EDITIONS
@import '../components/editions/editions';
@import '../components/editions/edition-item';

// SUBPAGES MENU
@import '../components/subpages-menu/subpages-menu-item';

// VALIDATORS ERROR COMPONENT
@import '../components/validator-error/validator-error';

// HEADER COMPONENTS
@import '../components/logo/logo';
@import '../components/search/search-popup';
@import '../components/main-nav-toggle/main-nav-toggle';
// @import '../components/main-nav-default/main-nav-default';
@import '../components/main-nav-aside/main-nav-aside';
@import '../components/header/header-nav';
//@import '../components/profile-nav/profile-nav';

// ON THE WEB
@import '../components/on-the-web/on-the-web';
@import '../components/on-the-web/on-the-web-carousel';
@import '../components/on-the-web/on-the-web-item';

// SUBSCRIPTIONS
@import '../components/subscriptions/subscriptions';
@import '../components/subscriptions/subscription-item';

// FOOTER COMPONENTS
@import '../components/footer/footer';
@import '../components/social/social-links';
@import '../components/site-info/site-info';
@import '../components/menu-cross/menu-cross';
@import '../components/partners/partners';
@import '../components/go-to-top/go-to-top';

// BASE COMPONENTS
@import '../components/menu-skip/menu-skip';
@import '../components/login-prompt/login-prompt';
@import '../components/header/header';
@import '../components/lang/lang';
//@import '../components/social/social-menu';
@import '../components/header/header-map';
//@import '../components/menu-floating/menu-floating';
@import '../components/steps/steps';
@import '../components/error-block/error-block';

// ITEMS
@import '../components/sitemap/sitemap-item';

// SECONDARY
@import '../components/stratis-menu/stratis-menu';

// BANNER STYLES
@import '../components/banner/banner';
@import '../components/banner/banner-item';

// GOOGLE TRANSLATE
@import '../components/google-translate/google-translate';

.lazy {
    background-image: image('placeholder-image.svg') !important;
}

iframe {
    border: 0 !important;
}

// STYLEGUIDE STYLES
// IMPORTANT, this file should be imported last.
@import './utils/utilities';

@if $enable-revenge == true {
    // @include revenge-css();
}
