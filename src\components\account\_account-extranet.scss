.account-extranet {
    display: flex;
    margin: 0 -50px;
    padding: 25px 0 17px;

    @include breakpoint(medium down) {
        flex-direction: column;
        padding-top: 0;
    }

    @include breakpoint(small down) {
        margin: 0 -20px;
        padding: 0 0 50px;
    }

    &__column {
        position: relative;
        width: 100%;

        & + & {
            flex-shrink: 0;
            position: relative;
            width: 50%;

            @include breakpoint(medium down) {
                margin-top: 90px;
                width: 100%;
            }

            @include breakpoint(small down) {
                margin-top: 80px;
            }
        }
    }

    .form__actions .btn {
        padding: 1.1em 1.5em 1em 1.5em;
    }

    .form__field-wrapper.is-inline {
        @include breakpoint(small down) {
            display: flex;
        }
    }

    &__wrapper {
        display: flex;
        flex-wrap: wrap;
        margin: 0 auto;
        max-width: 700px;
        padding: 0 75px;

        @include breakpoint(medium down) {
            padding: 0 50px;
        }

        @include breakpoint(small down) {
            max-width: 500px;
            padding: 0 20px;
        }

        .form {
            margin-bottom: 0;
        }
    }

    &__text {
        text-align: center;
        width: 100%;
    }

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-black));
        color: var(--color-1--1);
        line-height: 3.2rem;
        margin-bottom: 10px;

        @include breakpoint(small down) {
            font-size: 2.4rem;
            line-height: 2.6rem;
        }
    }

    &__description {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.25;
    }

    &__form {
        .btn[type="submit"] {
            font-weight: var(--fw-bold);
        }
    }

    .message {
        margin-bottom: 30px;
    }
}
