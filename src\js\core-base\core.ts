import Preloader from '@core/core-base/preloader';
import Polyfills from '@core/core-base/polyfills';
import ExternalLibraries from '@core/core-base/external-libraries';
import MediaLazyLoader from '@core/core-base/media-lazyload';
import ResponsiveEvent from '@core/core-base/responsive-event';
import TextSwitcherFactory from '@core/core-base/text-switcher';
import ToggleFactory from '@core/core-components/toggle';
import FixedElementFactory from '@core/core-components/fixed-element';
import TabsFactory from '@core/core-components/tabs';
import StratisTooltip from '@core/core-components/stratis-tooltip';
import FlashInfo from '@components/flash-info/flash-info';
import StratisVideoFactory from '@core/core-components/video';
// import MegamenuDefaultFactory from '@components/main-nav-default/main-nav-default';
import MegamenuFactory from '@components/main-nav-aside/main-nav-aside';
import HeaderMixFactory from '@components/header/header-nav';
import Breadcrumbs from '@components/breadcrumbs/breadcrumbs';
import MessageFactory from '@core/core-components/message';
import GeocodeFactory from '@core/core-components/geocode';
import FileInputFactory from '@core/core-components/file-input';
import StratisFormFactory from '@core/core-components/form';
import DateManagerFactory from '@core/core-components/date-manager';
import DateRangeFactory from '@core/core-components/date-range';
import FormValidatorFactory from '@core/core-components/form-validator';
import FormConditionManagerFactory from '@core/core-components/form-condition-manager';
import TimeManagerFactory from '@core/core-components/time-manager';
import DropAreaFactory, { DropArea } from '@components/drop-area/drop-area';
import StratisRatingFactory from '@components/rating/rating';
// import SiteFactory from '@core/core-components/sitefactory';
import handleMediaElements from '@components/media-element/media-element';
import { documentReady } from '@core/utils/common.utils';
import { isElementIntoView, isElementsExist } from '@core/utils/dom.utils';
import { addClass, hasClass } from '@core/utils/class.utils';
import PasswordVisibilityManagerFactory from '@components/password-visibility-manager/_password-visibility-manager';
import LocalStorage from '@core/core-base/local-storage';
import FavoritesSync from '@core/core-components/favorites-sync';
import { FavoritesAPI } from '@core/core-components/favorites-api';
import GluedDecorFactory from '@core/core-components/glued-decor';
import WidthAnimation from '@core/core-components/width-animation';
import FileInputLimitSizeFactory from '@core/core-components/file-input-limit-size';
import InlineSVGFactory from '@core/core-components/inline-svg';
import googleTranslateResponsiveHandler from '@components/google-translate/google-translate-responsive-handler';
import calculatePictureWidth from '@core/core-base/calculate-picture-width';
import initCustomMap from '@components/custom-map/custom-map';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
declare const tarteaucitron: any;

/**
 * Handle error event for document.
 * @param e
 */
const documentErrorHandler = (e): void => {
    const { target }: any = e;

    if (target.nodeName.toUpperCase() === 'IMG') {
        const wrapper = target.parentElement;
        const errorSource = target.getAttribute('data-error-src');

        if (errorSource) {
            target.src = errorSource;
        }

        if (wrapper?.tagName.toUpperCase() === 'PICTURE' && !hasClass(target, 'not-loaded')) {
            addClass(wrapper, 'not-loaded');
            addClass(target, 'not-loaded');

            const sources = [...wrapper.querySelectorAll('source')];

            if (sources.length) {
                sources.forEach(source => source.remove());
            }
        }
    }
};

/**
 * Handle load event for window.
 */
const windowLoadHandler = (): void => {
    // TODO: Enable on prod
    // if ('serviceWorker' in navigator) {
    //     const swPath = (window as any).swPath ? `${(window as any).swPath}/sw.js` : 'sw.js';
    //     navigator.serviceWorker.register(swPath);
    // }

    /**
     * Exclude:
     *  - lazyload images which noy loaded yet.
     *  - object-fit images
     */
    const siteImages: HTMLImageElement[] = [...document.querySelectorAll<HTMLImageElement>('img:not([data-src]):not([data-ofi-src])')]; // Edge fix

    siteImages
        // Exclude svg since they can return 0 sometimes.
        .filter(img => !img.src.includes('.svg'))
        .forEach(img => {
            if (!img.naturalHeight) {
                addClass(img, 'broken-img');
                img.setAttribute('height', '0');
            }
        });

    ToggleFactory.create('[data-sd-toggle]', {
        isWrapContent: false,
    });

    // Handle media-element iframes.
    handleMediaElements();

    GluedDecorFactory.create('.js-decor-glued');

    /**
     * Add here components that need to be updated after facets filtering
     * If component element is not changed then use update() method
     * Else init components one more times (check ToggleFactory example below)
     */
    window.addEventListener('facetsUpdate', () => {
        ToggleFactory.create('[data-sd-toggle]', {
            isWrapContent: false,
        });
    });

    const _ = new ResponsiveEvent();
    TabsFactory.create('.js-tabs');

    /**
     * Calculate scroll width for blocks with 100vw size (ce-gallery and discover)
     */
    const calculateScrollWidth = (): void => {
        const scrollWidth = window.innerWidth - document.documentElement.clientWidth;
        document.documentElement.style.setProperty('--scrollbar-width', ` ${scrollWidth}px`);
    };

    calculateScrollWidth();
    calculatePictureWidth();

    const _widthAnimation = new WidthAnimation();

    window.addEventListener('resize', () => {
        calculateScrollWidth();
        calculatePictureWidth();
    });

    // For dropdown in footer > subscriptions
    ToggleFactory.create('.subscription-item', {
        DOMElements: {
            dropdownBlock: '.subscription-item__dropdown',
            toggleBtn: '.subscription-item__toggle',
        },
    });
};

/**
 * Class represents dynamic iterface (as barrels)
 */
export default class StratisCore {
    /**
     * Load all core features
     */
    public static loadCore(): void {
        documentReady(() => {
            // Preload all images before PDF generation
            // window.isPrint - BE must add this var when page is start generate PDF
            // BE need add '-javascript-delay 5000' flag to wkhtmltopdf command (increase delay if needed)
            if ((window as any).isPrint) {
                (window as any).lazySizes.cfg.preloadAfterLoad = true;
            }

            // Init preloader
            Preloader.init();

            // Load polyfills
            Polyfills.init();

            // Dispatch example
            // StratisHelpers.dispatchEvent('responsive', true);

            // Load external JS libraries
            ExternalLibraries.init();

            // Load video/audio lazyloading
            MediaLazyLoader.init();

            const matches = {
                hasForm: document.querySelector('form'),
                hasFancybox: document.querySelector('[data-fancybox]'),
            };

            const _tooltip = new StratisTooltip('.js-tooltip:not([aria-disabled="true"]):not([disabled]):not(.is-disabled)');

            isElementIntoView('[data-fancybox]', async () => {
                const { default: Gallery } = await import('@core/core-components/gallery');
                const _ = new Gallery();
            });

            /**
             * Favorites
             */

            isElementsExist('.js-favorites', async el => {
                const { default: FavoritesFactory } = await import('@core/core-components/favorites');
                // Get storage for favorites
                const favoritesStorageName = 'stratis-favorites';
                const favoritesFromLocalStorage = localStorage.getItem(favoritesStorageName);
                const favoritesStorage = new LocalStorage(favoritesStorageName, {
                    defaultObject: JSON.parse(favoritesFromLocalStorage as string) || {},
                });

                // Create favorites api
                const favoritesAPI = new FavoritesAPI();

                // Sync favorites from local storage with BE
                const favoritesSync = new FavoritesSync(window.isUserAutorized || false, favoritesStorage, favoritesAPI);
                favoritesSync.sync();
                // Create favorites
                FavoritesFactory.create(el, {
                    isUserAutorized: window.isUserAutorized || false,
                    instancesInjection: {
                        storage: favoritesStorage,
                        api: favoritesAPI,
                    },
                });
            });

            if (matches.hasForm) {
                DropArea.handlePageFileDrop();
                StratisFormFactory.create('form');
                FormValidatorFactory.create('.js-validator-form');
                GeocodeFactory.create('.js-geocode');
                FileInputFactory.create('.js-file-field');
                FileInputLimitSizeFactory.create('.js-file-max-size');
                MessageFactory.create('.message');
                DateManagerFactory.create('input[type="date"]');
                DateRangeFactory.create('.search-facets__daterange');
                TimeManagerFactory.create('input[type="time"]');
                FormConditionManagerFactory.create('.js-condition');
                DropAreaFactory.create('.js-drop-area');
                StratisRatingFactory.create('.js-rating');
                PasswordVisibilityManagerFactory.create('.js-password-visibility-manager');

                isElementIntoView('.multiselect', async elements => {
                    const { default: MultiselectFactory } = await import('@core/core-components/multiselect');
                    MultiselectFactory.create(elements);
                });

                isElementIntoView('.js-autocomplete', async elements => {
                    const { default: AutocompleteFactory } = await import('@core/core-components/autocomplete');
                    AutocompleteFactory.create(elements);
                });
            }

            // Breadcrumbs init
            const _breadcrumbs = new Breadcrumbs();

            // Main
            FlashInfo.create('.flash-info');
            // const _menu = MegamenuDefaultFactory.createSingle('.js-main-nav');
            // Uncomment bottom lines to add HeaderMix logic
            const menu = MegamenuFactory.createSingle('.js-main-nav');
            HeaderMixFactory.createSingle('.js-nav-links', { instancesInjection: { menu } });
            FixedElementFactory.create('.js-fixed-element');
            StratisVideoFactory.create('.js-video');
            // SiteFactory.create('.js-sitefactory');

            isElementIntoView('.social-wall', async el => {
                const { default: SocialWallFactory } = await import('@components/social-wall/social-wall');
                SocialWallFactory.create(el);
            });

            isElementIntoView('.js-swiper', async el => {
                const { default: CarouselFactory } = await import('@core/core-components/carousel');
                CarouselFactory.create(el);
            });

            isElementIntoView('.js-dropdown', async el => {
                const { default: DropdownFactory } = await import('@core/core-components/dropdown');
                DropdownFactory.create(el, {
                    closeButton: {
                        enabled: false,
                    },
                    overlay: {
                        enabled: false,
                    },
                });
            });

            // Secondary
            TextSwitcherFactory.create('[data-ts-alt-text]');

            // Select the scroll-to-top button
            const scrollToTopBtn = document.querySelector('.go-to-top__link') as HTMLElement;

            if (scrollToTopBtn) {
                window.addEventListener('scroll', () => {
                    const isVisible = window.scrollY > 300;
                    // Add or remove the 'is-hidden' class based on visibility
                    scrollToTopBtn.classList.toggle('is-hidden', !isVisible);

                    const scrollPosition = window.scrollY + window.innerHeight;
                    const pageHeight = document.documentElement.scrollHeight;

                    if (pageHeight - scrollPosition <= 100) {
                        // On est proche du bas de la page
                        scrollToTopBtn.style.bottom = '297px';
                    } else {
                        // On est plus haut que 100px du bas
                        scrollToTopBtn.style.bottom = '0'; // Valeur par défaut à adapter
                    }
                });
            }
            initCustomMap();
            window.addEventListener('resize', resizeScreen);
            resizeScreen();
        });

        // Initialization InlineSVG
        InlineSVGFactory.create('.inline-svg');

        googleTranslateResponsiveHandler();

        document.addEventListener('error', documentErrorHandler, true);
        window.addEventListener('load', windowLoadHandler);
    }
}

function resizeScreen(): void {
    window.addEventListener('resize', manageQuickaccessDisplay);
    manageQuickaccessDisplay();
}

function manageQuickaccessDisplay(): void {
    const width = window.innerWidth;
    const heroItems = document.querySelectorAll('.hero-item');
    const quicklinksInfo = document.querySelector('.quicklinks-info');
    const quicklinksHomeSectionContent = document.querySelector('.quicklinks-home-section-content');

    if (!quicklinksInfo) {
        return;
    }

    if (width > 767) {
        // ✅ Si tu passes en desktop, vérifie si l’original existe encore
        if (quicklinksHomeSectionContent && !quicklinksHomeSectionContent.contains(quicklinksInfo)) {
            quicklinksHomeSectionContent.appendChild(quicklinksInfo);
        }
        // Supprime les copies déjà présentes dans chaque hero-item
        heroItems.forEach(item => {
            const heroItemContent = item.querySelector('.hero-item__content');
            if (heroItemContent) {
                heroItemContent.querySelectorAll('.quicklinks-info').forEach(node => node.remove());
            }
        });
    } else {
        heroItems.forEach(item => {
            const heroItemContent = item.querySelector('.hero-item__content');
            if (heroItemContent) {
                // ✅ Supprime les quicklinks-info déjà présents
                heroItemContent.querySelectorAll('.quicklinks-info').forEach(node => node.remove());

                // ✅ Clone et insère
                const clone = quicklinksInfo.cloneNode(true);
                heroItemContent.appendChild(clone);
            }
        });

        // ✅ Supprime l’original pour qu’il ne reste pas ailleurs
        if (quicklinksInfo.parentElement) {
            quicklinksInfo.parentElement.removeChild(quicklinksInfo);
        }
    }
}
