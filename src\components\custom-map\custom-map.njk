{% set customMapData = [
    {
        title: 'Infospot 1',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 835,
            z: -3956
        }
    },
    {
        title: 'Infospot 2',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 460,
            z: -1520
        }
    },
    {
        title: 'Infospot 3',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 760,
            z: -1065
        }
    },
    {
        title: 'Infospot 4',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 620,
            z: -620
        }
    },
    {
        title: 'Infospot 5',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 745,
            z: -5
        }
    },
    {
        title: 'Infospot 6',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 695,
            z: 635
        }
    },
    {
        title: 'Infospot 7',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 535,
            z: 1353
        }
    },
    {
        title: 'Infospot 8',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 565,
            z: 2490
        }
    },
    {
        title: 'Infospot 9',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 5000,
            y: 470,
            z: 4180
        }
    },
    {
        title: 'Infospot 10',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 399,
            y: 755,
            z: 5000
        }
    },
    {
        title: 'Infospot 11',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -572,
            y: 390,
            z: 5000
        }
    },
    {
        title: 'Infospot 12',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -1760,
            y: 405,
            z: 5000
        }
    },
    {
        title: 'Infospot 13',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -3577,
            y: 450,
            z: 5000
        }
    },
    {
        title: 'Infospot 14',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 425,
            z: 2794
        }
    },
    {
        title: 'Infospot 15',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 640,
            z: 1642
        }
    },
    {
        title: 'Infospot 16',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 765,
            z: 970
        }
    },
    {
        title: 'Infospot 17',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 445,
            z: -1010
        }
    },
    {
        title: 'Infospot 18',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 375,
            z: -1825
        }
    },
    {
        title: 'Infospot 19',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 850,
            z: -2140
        }
    },
    {
        title: 'Infospot 20',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 385,
            z: -2445
        }
    },
    {
        title: 'Infospot 21',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -5000,
            y: 890,
            z: -2800
        }
    },
    {
        title: 'Infospot 22',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -4510,
            y: 930,
            z: -5000
        }
    },
    {
        title: 'Infospot 23',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -2490,
            y: 675,
            z: -5000
        }
    },
    {
        title: 'Infospot 24',
        image: 'images/mappoint-1.svg',
        coords: {
            x: -1191,
            y: 780,
            z: -5000
        }
    },
    {
        title: 'Infospot 25',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 145,
            y: 615,
            z: -5000
        }
    },
    {
        title: 'Infospot 26',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 1290,
            y: 635,
            z: -5000
        }
    },
    {
        title: 'Infospot 27',
        image: 'images/mappoint-1.svg',
        coords: {
            x: 2535,
            y: 825,
            z: -5000
        }
    }
] %}

{% macro customMap() %}
    <section class="custom-map-section">
        <div class="custom-map-wrapper js-custom-map-wrapper">
            <h2 class="custom-map-title js-custom-map-title"><span>Découvrez</span>le campus</h2>
            <div class="custom-map-pictogram js-custom-map-pictogram">
                <img src="images/custom-map-pictogram.png" alt="Changer l’orientation de votre support en mode paysage pour une lecture plus confortable du panoramique à 360° du campus">
            </div>
            <div id="customMap" class="custom-map js-custom-map">
                <button class="custom-map__btn js-custom-map-button" data-customMap="images/slider_360_with_lines.jpg" style="background-image: url(images/360.jpg)">
                    <span class="ghost">Preview in 360</span>
                </button>
                <div class="custom-map-popup js-custom-map-popup">
                    <button type="button" class="custom-map-popup__close js-custom-map-popup-close" data-fa-icon="&#xf00d;">
                        <span class="ghost">Fermer</span>
                    </button>
                    <section class="custom-map-popup__content custom-map-content js-custom-map-popup-content"></section>
                </div>
            </div>

            <div class="custom-map-data js-custom-map-data">
                {%- for infospot in customMapData -%}
                    <div class="custom-map-infospot js-infospot" id="infospot-id-{{ loop.index }}"
                        data-infospot-image="{{ infospot.image }}"
                        data-infospot-coords="{{ infospot.coords.x }},{{ infospot.coords.y }},{{ infospot.coords.z }}"
                    >
                        <div class="js-infospot-title" data-infospot-target="infospot-id-{{ loop.index }}">
                            <div class="infospot-content">
                                <ul>
                                    <li>{{ infospot.title }}</li>
                                    {%- if loop.first -%}
                                        <li>Athlétisme</li>
                                        <li>Aviron</li>
                                        <li>Canoë-Kayak</li>
                                        <li>Handisport</li>
                                        <li>Pentathlon moderne</li>
                                    {% else %}
                                        {%- for item in range(2, range(3, 8) | random) -%}
                                            <li>random feature</li>
                                        {%- endfor -%}
                                    {%- endif -%}
                                </ul>
                            </div>
                        </div>
                        <div class="custom-map-infospot__content custom-map-content js-infospot-content">
                            <div class="custom-map-content__inner">
                                <h2 class="custom-map-content__title"><span>Halle - INFOSPOT {{ loop.index }}</span> Joseph Maigrot</h2>
                                <p class="custom-map-content__teaser">Mauris mollis, lacus sed tincidunt suscipit, enim felis ullamcorper ex, id hendrerit lacus.</p>
                                <div class="custom-map-content__body rte">
                                    <p>{{ lorem(1) }}</p>
                                    <ul>
                                        <li>{{ lorem(1) }}</li>
                                        <li>{{ lorem(1) }}</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="custom-map-content__button">
                                <a href="#" class="btn" data-fa-icon="&#xf105;">en savoir plus</a>
                            </div>
                        </div>
                    </div>
                {%- endfor -%}
            </div>
        </div>
    </section>
{% endmacro %}
